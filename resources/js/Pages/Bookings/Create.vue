<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useForm, router } from '@inertiajs/vue3';
import axios from 'axios';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
const route = window.route;
import DashboardLayout from '@/Layouts/DashboardLayout.vue';
import Card from '@/Components/Card.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import Alert from '@/Components/Alert.vue';

// No longer need axios since we're calculating prices locally

const props = defineProps({
  accommodation: Object,
  bookingStatuses: Array,
  existingBookings: Array,
});

// Form for booking
const form = useForm({
  accommodation_id: props.accommodation.id,
  user_id: null, // Will be set to the current user
  email: '',
  contact_number: '',
  first_name: '',
  last_name: '',
  start_date: '',
  end_date: '',
  occupancy: props.accommodation.min_occupancy || 1,
  total_price: 0,
  booking_status_id: null,
  notes: '',
});

// Date range picker state
const dateRange = ref(null);

// Availability check state
const checkingAvailability = ref(false);
const availabilityResult = ref(null);
const availabilityError = ref(null);

// Price calculation state
const priceDetails = ref(null);

// Booked dates for the accommodation
const disabledDates = ref(props.existingBookings || []);

// Unavailable periods from the accommodation
const unavailablePeriods = ref([]);

// Booking status
const confirmedStatus = computed(() => {
  return props.bookingStatuses.find(status => status.name === 'Confirmed');
});

// Minimum stay requirement
const minStay = computed(() => props.accommodation.minimum_stay || 1);

// Set default booking status to Confirmed
onMounted(() => {
  if (confirmedStatus.value) {
    form.booking_status_id = confirmedStatus.value.id;
  }

  // Convert string dates to Date objects for the DateRangePicker
  if (props.existingBookings && props.existingBookings.length > 0) {
    // For Vue 3 Date Picker, we need to provide a function that returns true for disabled dates
    const bookingRanges = props.existingBookings.map(booking => ({
      start: new Date(booking.start),
      end: new Date(booking.end)
    }));

    // Store the ranges for our own validation
    disabledDates.value = bookingRanges;
  }

  // Fetch unavailable periods
  fetchUnavailablePeriods();
});

// Watch for date range changes to validate and calculate price
watch(dateRange, (newValue) => {
  // Always clear previous errors when date range changes
  form.clearErrors();
  availabilityError.value = null;

  // Reset form state when dates are cleared
  if (!newValue || !Array.isArray(newValue) || newValue.length < 2) {
    form.start_date = '';
    form.end_date = '';
    priceDetails.value = null;
    availabilityResult.value = null;
    return;
  }

  // Format dates for the form
  form.start_date = formatDate(newValue[0]);
  form.end_date = formatDate(newValue[1]);

  // Validate date range
  const startDate = new Date(form.start_date);
  const endDate = new Date(form.end_date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Check if start date is in the past
  if (startDate < today) {
    form.setError('start_date', 'The start date cannot be in the past.');
    return;
  }

  // Check if end date is before start date
  if (endDate < startDate) {
    form.setError('end_date', 'The end date must be after the start date.');
    return;
  }

  // Check minimum stay requirement
  const nights = (endDate - startDate) / (1000 * 60 * 60 * 24);
  if (nights < minStay.value) {
    form.setError('end_date', `You must book at least ${minStay.value} nights.`);
    return;
  }

  // Check minimum booking notice
  const minDate = getMinDate();
  if (startDate < minDate) {
    form.setError('start_date', `This accommodation requires bookings to be made at least ${props.accommodation.minimum_booking_notice} days in advance.`);
    return;
  }

  // Check for overlapping bookings or unavailable date ranges
  const isOverlapping = disabledDates.value.some(range => {
    const rangeStart = new Date(range.start);
    const rangeEnd = new Date(range.end);

    // Check if the selected range overlaps with any existing booking or unavailable date range
    return (startDate <= rangeEnd && endDate >= rangeStart);
  });

  if (isOverlapping) {
    form.setError('start_date', 'The selected dates overlap with an existing booking or unavailable period.');
    return;
  }

  // Check for recurring unavailable days
  if (unavailablePeriods.value.length > 0) {
    try {
      // Loop through each day in the selected range
      const currentDate = new Date(startDate);
      let hasUnavailableDay = false;
      let unavailableDates = [];

      while (currentDate <= endDate) {
        const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 6 = Saturday

        // Check if this day of week is in any unavailable period
        for (const period of unavailablePeriods.value) {
          if (period.days_of_week && Array.isArray(period.days_of_week)) {
            // Convert to numbers for comparison since they might be strings from the API
            const daysOfWeek = period.days_of_week.map(d => parseInt(d));
            if (daysOfWeek.includes(dayOfWeek)) {
              hasUnavailableDay = true;
              unavailableDates.push(new Date(currentDate));
              break;
            }
          }
        }

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1);
      }

      if (hasUnavailableDay) {
        // Format the unavailable dates for display
        const formattedDates = unavailableDates.map(date =>
          date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })
        ).join(', ');

        form.setError('start_date', `The selected dates include days that are marked as unavailable (${formattedDates}).`);
        return;
      }
    } catch (error) {
      console.error('Error checking unavailable days:', error);
      form.setError('start_date', 'An error occurred while checking availability. Please try different dates.');
      return;
    }
  }

  // If all validation passes, calculate price
  calculatePrice();
}, { deep: true });

// Watch for occupancy changes to recalculate price
watch(() => form.occupancy, () => {
  if (form.start_date && form.end_date) {
    calculatePrice();
  }
});

// Format date to YYYY-MM-DD
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

// Calculate price for the selected dates locally
const calculatePrice = () => {
  if (!form.start_date || !form.end_date) return;

  checkingAvailability.value = true;
  availabilityResult.value = null;
  availabilityError.value = null;

  try {
    // Check if the accommodation has any pricing set
    const prices = props.accommodation.prices || [];
    const hasDefaultPrice = prices.some(price => price.type === 'default');

    if (!hasDefaultPrice) {
      form.setError('start_date', 'This accommodation does not have a default price set. Bookings cannot be made until pricing is configured.');
      availabilityError.value = 'No pricing configured';
      return;
    }

    // Calculate daily prices and total
    const startDate = new Date(form.start_date);
    const endDate = new Date(form.end_date);
    const dailyPrices = {};
    let totalPrice = 0;

    // Loop through each day in the date range
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0];

      // Get the price for this date based on the accommodation's pricing data
      const price = getPriceForDate(dateStr, form.occupancy);

      // If any day has a zero price, show an error
      if (price <= 0) {
        form.setError('start_date', 'One or more selected dates do not have valid pricing. Please contact the accommodation owner.');
        availabilityError.value = 'Invalid pricing';
        return;
      }

      dailyPrices[dateStr] = price;
      totalPrice += price;

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Set the price information
    form.total_price = totalPrice;
    priceDetails.value = {
      daily_prices: dailyPrices,
      total_price: totalPrice,
      currency: 'R',
      occupancy: form.occupancy,
      min_occupancy: props.accommodation.min_occupancy,
      max_occupancy: props.accommodation.max_occupancy
    };

    // Create a success result for display
    availabilityResult.value = {
      available: true,
      total_price: totalPrice,
      pricing: priceDetails.value
    };
  } catch (error) {
    console.error('Price calculation error:', error);
    form.setError('start_date', 'An error occurred while calculating the price. Please try again.');
  } finally {
    checkingAvailability.value = false;
  }
};

// Get price for a specific date based on the accommodation's pricing data
const getPriceForDate = (dateStr, occupancy) => {
  // Get the accommodation's pricing data
  const prices = props.accommodation.prices || [];

  // First check for date-specific pricing
  const datePrice = prices.find(price =>
    price.type === 'date_range' &&
    new Date(price.start_date) <= new Date(dateStr) &&
    new Date(price.end_date) >= new Date(dateStr)
  );

  if (datePrice) {
    return calculatePriceWithOccupancy(datePrice.price, occupancy, datePrice.additional_person_price);
  }

  // Then check for day-of-week pricing
  const dayOfWeek = new Date(dateStr).getDay().toString();
  const dayPrice = prices.find(price =>
    price.type === 'day_of_week' &&
    price.day_of_week === dayOfWeek
  );

  if (dayPrice) {
    return calculatePriceWithOccupancy(dayPrice.price, occupancy, dayPrice.additional_person_price);
  }

  // Finally, return default price
  const defaultPrice = prices.find(price => price.type === 'default');
  const basePrice = defaultPrice ? defaultPrice.price : 0;
  const additionalPersonPrice = defaultPrice ? defaultPrice.additional_person_price : 0;

  return calculatePriceWithOccupancy(basePrice, occupancy, additionalPersonPrice);
};

// Calculate price based on occupancy
const calculatePriceWithOccupancy = (basePrice, occupancy, additionalPersonPrice = 0) => {
  const minOccupancy = props.accommodation.min_occupancy || 1;
  const maxOccupancy = props.accommodation.max_occupancy || 10;

  // If occupancy is less than minimum, use minimum occupancy price
  if (occupancy < minOccupancy) {
    return basePrice;
  }

  // If occupancy is greater than maximum, cap at maximum
  const effectiveOccupancy = Math.min(occupancy, maxOccupancy);

  // Calculate additional price for extra occupants
  const extraOccupants = effectiveOccupancy - minOccupancy;

  if (extraOccupants <= 0) {
    return basePrice;
  }

  // Calculate additional price
  const additionalPrice = extraOccupants * (additionalPersonPrice || 0);

  return basePrice + additionalPrice;
};

// Submit the booking form
const submitForm = () => {
  form.post(route('accommodations.bookings.store', props.accommodation.id), {
    onSuccess: () => {
      router.visit(route('accommodations.show', props.accommodation.id));
    },
  });
};

// Calculate minimum date based on accommodation's minimum booking notice
const getMinDate = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Apply minimum booking notice if available
  if (props.accommodation.minimum_booking_notice > 0) {
    const minDays = props.accommodation.minimum_booking_notice;
    return new Date(today.getTime() + (minDays * 24 * 60 * 60 * 1000));
  }

  return today;
};

// Format currency
const formatCurrency = (amount) => {
  return `R${amount.toFixed(2)}`;
};

// Function to check if a date is disabled
const isDateDisabled = (date) => {
  // Check if the date falls within any of the disabled date ranges
  const isInDisabledRange = disabledDates.value.some(range => {
    const rangeStart = new Date(range.start);
    const rangeEnd = new Date(range.end);

    // Set time to 00:00:00 for accurate date comparison
    rangeStart.setHours(0, 0, 0, 0);
    rangeEnd.setHours(0, 0, 0, 0);

    const checkDate = new Date(date);
    checkDate.setHours(0, 0, 0, 0);

    // Check if the date is within the range (inclusive)
    return checkDate >= rangeStart && checkDate <= rangeEnd;
  });

  // If already in a disabled range, return true
  if (isInDisabledRange) return true;

  // Check minimum stay requirement for end dates
  if (dateRange.value && Array.isArray(dateRange.value) && dateRange.value.length >= 1 && dateRange.value[0]) {
    const startDate = new Date(dateRange.value[0]);
    const candidateDate = new Date(date);
    
    // Set time to 00:00:00 for accurate date comparison
    startDate.setHours(0, 0, 0, 0);
    candidateDate.setHours(0, 0, 0, 0);
    
    // If this could be an end date, check if it meets minimum stay
    if (candidateDate > startDate) {
      const diffDays = (candidateDate - startDate) / (1000 * 60 * 60 * 24);
      if (diffDays < minStay.value) {
        return true;
      }
    }
  }

  return false;
};

// Fetch unavailable periods for the accommodation
const fetchUnavailablePeriods = async () => {
  try {
    const response = await axios.get(route('accommodations.unavailable-periods.index', props.accommodation.id));

    if (response.data.success && response.data.periods) {
      // Process date range unavailable periods
      const dateRangePeriods = response.data.periods
        .filter(period => period.type === 'date_range' && period.active)
        .map(period => ({
          start: new Date(period.start_date),
          end: new Date(period.end_date)
        }));

      // Process recurring days unavailable periods
      const recurringDaysPeriods = response.data.periods
        .filter(period => period.type === 'recurring_days' && period.active);

      // Store recurring days periods for validation
      unavailablePeriods.value = recurringDaysPeriods;

      // Generate disabled dates for recurring days (for the next 6 months)
      const recurringDisabledDates = [];
      if (recurringDaysPeriods.length > 0) {
        const today = new Date();
        const sixMonthsLater = new Date(today);
        sixMonthsLater.setMonth(today.getMonth() + 6);

        // Loop through each day in the next 6 months
        const currentDate = new Date(today);
        while (currentDate <= sixMonthsLater) {
          const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 6 = Saturday

          // Check if this day of week is in any unavailable period
          for (const period of recurringDaysPeriods) {
            if (period.days_of_week && Array.isArray(period.days_of_week)) {
              const daysOfWeek = period.days_of_week.map(d => parseInt(d));
              if (daysOfWeek.includes(dayOfWeek)) {
                // Add this date to disabled dates
                recurringDisabledDates.push({
                  start: new Date(currentDate),
                  end: new Date(currentDate)
                });
                break;
              }
            }
          }

          // Move to the next day
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // Store all disabled date ranges for our own validation
      disabledDates.value = [
        ...disabledDates.value,
        ...dateRangePeriods,
        ...recurringDisabledDates
      ];
    }
  } catch (error) {
    console.error('Error fetching unavailable periods:', error);
  }
};
</script>

<template>
  <DashboardLayout :title="`Add Booking - ${accommodation.name}`">
    <template #header>
      <h2 class="font-semibold text-xl text-primary leading-tight">
        Add New Booking for {{ accommodation.name }}
      </h2>
    </template>

    <div class="py-6 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto">
        <Card shadow="sm">
          <template #header>
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium">Booking Details</h3>
              <PrimaryButton
                as="Link"
                :href="route('accommodations.show', accommodation.id)"
                variant="light"
              >
                Back to Accommodation
              </PrimaryButton>
            </div>
          </template>

          <form @submit.prevent="submitForm">
            <!-- Date Selection -->
            <div class="mb-6">
              <InputLabel for="date-range" value="Booking Dates" />

              <div class="mt-2 relative">
                <VueDatePicker
                  v-model="dateRange"
                  range
                  :min-date="getMinDate()"
                  :disabled-dates="isDateDisabled"
                  :min-range-length="minStay"
                  placeholder="Select booking dates"
                  :enable-time-picker="false"
                  auto-apply
                  :clearable="true"
                  class="brand-datepicker"
                  :preview-format="(date) => new Date(date).toLocaleDateString('en-ZA')"
                  :format="'dd/MM/yyyy'"
                  :week-start="1"
                  :month-name-format="'long'"
                  :day-names-format="'short'"
                  multi-calendars
                  :calendar-cell-class-name="'dp__calendar-item'"
                  :disabled-dates-class-name="'booked-date'"
                />
              </div>

              <InputError :message="form.errors.start_date || form.errors.end_date" class="mt-2" />

              <p v-if="unavailablePeriods.value && unavailablePeriods.value.length > 0" class="mt-2 text-sm text-gray-500">
                <span class="font-medium text-amber-600">Note:</span> This accommodation has recurring unavailable days:
                <span v-for="(period, index) in unavailablePeriods.value" :key="index">
                  {{ period.days_of_week && Array.isArray(period.days_of_week) ?
                    period.days_of_week.map(day => ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][parseInt(day)]).join(', ') : '' }}
                  {{ index < unavailablePeriods.value.length - 1 ? ' and ' : '' }}
                </span>
              </p>
            </div>

            <!-- Price Information -->
            <div v-if="checkingAvailability" class="mb-6">
              <div class="flex justify-center items-center py-4">
                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-accent-primary rounded-full" role="status" aria-label="loading">
                  <span class="sr-only">Loading...</span>
                </div>
                <span class="ml-2">Calculating price...</span>
              </div>
            </div>

            <div v-if="availabilityResult && availabilityResult.available" class="mb-6">
              <Alert
                type="success"
                title="Dates Available"
                dismissible
              >
                <p>The selected dates are available for booking.</p>
              </Alert>
            </div>

            <div v-if="availabilityError && availabilityError === 'No pricing configured'" class="mb-6">
              <Alert
                type="danger"
                title="Pricing Not Configured"
                dismissible
              >
                <p>This accommodation does not have a default price set. Bookings cannot be made until pricing is configured.</p>
              </Alert>
            </div>

            <div v-if="availabilityError && availabilityError === 'Invalid pricing'" class="mb-6">
              <Alert
                type="warning"
                title="Invalid Pricing"
                dismissible
              >
                <p>One or more selected dates do not have valid pricing. Please contact the accommodation owner.</p>
              </Alert>
            </div>

            <div v-if="form.start_date && form.end_date">
              <!-- Guest Information -->
              <div class="mb-6">
                <h4 class="text-lg font-medium mb-4">Guest Information</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- First Name -->
                  <div>
                    <InputLabel for="first_name" value="First Name" />
                    <TextInput
                      id="first_name"
                      v-model="form.first_name"
                      type="text"
                      class="mt-1 block w-full"
                      required
                    />
                    <InputError :message="form.errors.first_name" class="mt-2" />
                  </div>

                  <!-- Last Name -->
                  <div>
                    <InputLabel for="last_name" value="Last Name" />
                    <TextInput
                      id="last_name"
                      v-model="form.last_name"
                      type="text"
                      class="mt-1 block w-full"
                      required
                    />
                    <InputError :message="form.errors.last_name" class="mt-2" />
                  </div>

                  <!-- Email -->
                  <div>
                    <InputLabel for="email" value="Email" />
                    <TextInput
                      id="email"
                      v-model="form.email"
                      type="email"
                      class="mt-1 block w-full"
                      required
                    />
                    <InputError :message="form.errors.email" class="mt-2" />
                  </div>

                  <!-- Contact Number -->
                  <div>
                    <InputLabel for="contact_number" value="Contact Number" />
                    <TextInput
                      id="contact_number"
                      v-model="form.contact_number"
                      type="text"
                      class="mt-1 block w-full"
                      required
                    />
                    <InputError :message="form.errors.contact_number" class="mt-2" />
                  </div>
                </div>
              </div>

              <!-- Booking Details -->
              <div class="mb-6">
                <h4 class="text-lg font-medium mb-4">Booking Details</h4>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Occupancy -->
                  <div>
                    <InputLabel for="occupancy" value="Number of Guests" />
                    <TextInput
                      id="occupancy"
                      v-model="form.occupancy"
                      type="number"
                      :min="accommodation.min_occupancy || 1"
                      :max="accommodation.max_occupancy || 10"
                      class="mt-1 block w-full"
                      required
                    />
                    <p class="mt-1 text-sm text-gray-500">
                      Min: {{ accommodation.min_occupancy || 1 }}, Max: {{ accommodation.max_occupancy || 10 }}
                    </p>
                    <InputError :message="form.errors.occupancy" class="mt-2" />
                  </div>

                  <!-- Booking Status -->
                  <div>
                    <InputLabel for="booking_status_id" value="Booking Status" />
                    <select
                      id="booking_status_id"
                      v-model="form.booking_status_id"
                       class="py-3 px-4 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800 focus:border-accent-primary focus:ring-accent-primary disabled:opacity-50 disabled:pointer-events-none"
                      required
                    >
                      <option v-for="status in bookingStatuses" :key="status.id" :value="status.id">
                        {{ status.name }}
                      </option>
                    </select>
                    <InputError :message="form.errors.booking_status_id" class="mt-2" />
                  </div>

                  <!-- Booking Notes -->
                  <div class="md:col-span-2">
                    <InputLabel for="notes" value="Booking Notes" />
                    <textarea
                      id="notes"
                      v-model="form.notes"
                      rows="4"
                      class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-accent-primary focus:ring-accent-primary disabled:opacity-50 disabled:pointer-events-none dark:bg-slate-900 dark:border-gray-700 dark:text-gray-400"
                      placeholder="Add any special requests or additional information about the booking"
                    ></textarea>
                    <InputError :message="form.errors.notes" class="mt-2" />
                  </div>
                </div>
              </div>

              <!-- Price Breakdown -->
              <div v-if="priceDetails" class="mb-6">
                <h4 class="text-lg font-medium mb-4">Price Breakdown</h4>

                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                  <div class="space-y-2">
                    <div v-for="(price, date) in priceDetails.daily_prices" :key="date" class="flex justify-between">
                      <span>{{ new Date(date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }) }}</span>
                      <span>{{ formatCurrency(price) }}</span>
                    </div>

                    <div class="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                      <div class="flex justify-between font-semibold text-lg">
                        <span>Total Price</span>
                        <span class="text-accent-primary">{{ formatCurrency(priceDetails.total_price) }}</span>
                      </div>
                      <p class="text-sm text-gray-500 mt-1">
                        Automatically calculated based on dates and occupancy
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Submit Button -->
              <div class="flex justify-end space-x-3">
                <PrimaryButton
                  as="Link"
                  :href="route('accommodations.show', accommodation.id)"
                  variant="light"
                >
                  Cancel
                </PrimaryButton>
                <PrimaryButton
                  type="submit"
                  :disabled="form.processing || form.errors.start_date || !priceDetails || priceDetails.total_price <= 0"
                  :loading="form.processing"
                >
                  Create Booking
                </PrimaryButton>
              </div>
            </div>
          </form>
        </Card>
      </div>
    </div>
  </DashboardLayout>
</template>

<style>
/* Custom styling for the Vue Date Picker */
.brand-datepicker {
  width: 100%;
}

/* Make the datepicker popup wider */
.brand-datepicker .dp__main {
  width: 100%;
  max-width: 100%;
}

.brand-datepicker .dp__menu {
  width: 100%;
  max-width: 100%;
}

/* Ensure the calendar grid expands to fill the available space */
.brand-datepicker .dp__instance_calendar {
  width: 100%;
}

/* Adjust the multi-calendar layout */
.brand-datepicker .dp__menu_inner {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

/* Make each calendar in multi-calendar mode take up equal space */
.brand-datepicker .dp__instance_calendar {
  flex: 1;
  min-width: 0; /* Allow flex items to shrink below content size */
  margin: 0 4px;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .brand-datepicker .dp__menu_inner {
    flex-direction: column;
  }

  .brand-datepicker .dp__instance_calendar {
    margin: 8px 0;
  }
}

/* Adjust the month picker to be wider */
.brand-datepicker .dp__month_year_select {
  width: 100%;
}

/* Make sure the calendar cells expand properly */
.brand-datepicker .dp__calendar_item {
  width: 14.28%; /* 100% ÷ 7 days */
}

.brand-datepicker .dp__theme_light {
  --dp-primary-color: #003B5C;
  --dp-secondary-color: #009B77;
  --dp-success-color: #009B77;
  --dp-icon-color: #003B5C;
  --dp-border-color: #e5e7eb;
  --dp-menu-border-color: #e5e7eb;
  --dp-border-color-hover: #003B5C;
  --dp-disabled-color: #f3f4f6;
  --dp-disabled-color-text: #9ca3af;
  --dp-hover-color: #E5F3FF;
  --dp-hover-text-color: #003B5C;
  --dp-hover-icon-color: #003B5C;
  --dp-range-between-dates-background-color: #E5F3FF;
  --dp-range-between-dates-text-color: #003B5C;
}

/* Dark mode overrides */
.dark .brand-datepicker .dp__theme_dark {
  --dp-primary-color: #009B77;
  --dp-secondary-color: #009B77;
  --dp-success-color: #009B77;
  --dp-icon-color: #009B77;
  --dp-hover-color: rgba(0, 155, 119, 0.1);
  --dp-hover-text-color: #009B77;
  --dp-hover-icon-color: #009B77;
  --dp-range-between-dates-background-color: rgba(0, 155, 119, 0.1);
  --dp-range-between-dates-text-color: #009B77;
}
</style>
