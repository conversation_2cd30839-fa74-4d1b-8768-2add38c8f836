<?php

namespace App\Services;

use App\Models\Accommodation;
use App\Models\Booking;
use Illuminate\Support\Carbon;

class BookingPriceService
{
    /**
     * Calculate the total price for a booking based on accommodation, date range, and occupancy.
     *
     * @param Booking $booking The booking to calculate the price for
     * @return float The calculated total price
     * @throws \Exception If no default price is set for the accommodation
     */
    public function calculateTotalPrice(Booking $booking): float
    {
        // Make sure we have the required data
        if (!$booking->accommodation_id || !$booking->start_date || !$booking->end_date || !$booking->occupancy) {
            return 0;
        }

        // Load the accommodation if not already loaded
        if (!$booking->relationLoaded('accommodation')) {
            $booking->load('accommodation');
        }

        $accommodation = $booking->accommodation;

        // Check if the accommodation has a default price set
        $hasDefaultPrice = $accommodation->prices()->where('type', 'default')->exists();

        // If no individual default price, check if the accommodation belongs to a group with a default price
        if (!$hasDefaultPrice && $accommodation->accommodation_group_id) {
            // Load the group relationship if not already loaded
            if (!$accommodation->relationLoaded('group')) {
                $accommodation->load('group');
            }

            $hasGroupDefaultPrice = $accommodation->group->prices()
                ->where('type', 'default')
                ->exists();

            // Use group pricing if available
            $hasDefaultPrice = $hasGroupDefaultPrice;
        }

        if (!$hasDefaultPrice) {
            throw new \Exception('This accommodation does not have either an individual default price or a group default price. Bookings cannot be made until pricing is configured.');
        }

        $startDate = Carbon::parse($booking->start_date);
        $endDate = Carbon::parse($booking->end_date);
        $occupancy = $booking->occupancy;

        $totalPrice = $this->calculatePriceForDateRange($accommodation, $startDate, $endDate, $occupancy);

        // Ensure the total price is greater than zero
        if ($totalPrice <= 0) {
            throw new \Exception('The calculated price is invalid (zero or negative). Please check the accommodation pricing configuration.');
        }

        return $totalPrice;
    }

    /**
     * Calculate the total price for a date range and occupancy.
     *
     * @param Accommodation $accommodation The accommodation to calculate the price for
     * @param Carbon $startDate The start date of the booking
     * @param Carbon $endDate The end date of the booking
     * @param int $occupancy The number of occupants
     * @return float The calculated total price
     */
    public function calculatePriceForDateRange(Accommodation $accommodation, Carbon $startDate, Carbon $endDate, int $occupancy): float
    {
        $totalPrice = 0;

        // Calculate price for each night in the date range
        // A stay from 2023-06-01 to 2023-06-05 is 4 nights, not 5 days
        $currentDate = clone $startDate;

        // Only iterate until the day before the end date (to count nights, not days)
        while ($currentDate < $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            // Use the existing method from Accommodation model
            $dailyPrice = $accommodation->getPriceForDate($dateStr, $occupancy);
            $totalPrice += $dailyPrice;
            $currentDate->addDay();
        }

        return $totalPrice;
    }
}
