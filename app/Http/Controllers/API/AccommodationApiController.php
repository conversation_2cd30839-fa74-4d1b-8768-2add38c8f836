<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Accommodation;
use App\Models\AccommodationGroup;
use App\Services\AvailabilityChecker;
use App\Services\DiscountService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Accommodations",
 *     description="API endpoints for managing accommodations"
 * )
 */
class AccommodationApiController extends Controller
{
    protected $availabilityChecker;
    protected $discountService;

    public function __construct(AvailabilityChecker $availabilityChecker, DiscountService $discountService)
    {
        $this->availabilityChecker = $availabilityChecker;
        $this->discountService = $discountService;
    }

    /**
     * Get a list of accommodations for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations",
     *     summary="Get list of accommodations",
     *     description="Returns a list of accommodations belonging to the authenticated user, with optional filtering by group",
     *     operationId="getAccommodations",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\Parameter(
     *         name="group_id",
     *         in="query",
     *         description="Filter by accommodation group ID",
     *         required=false,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="published",
     *         in="query",
     *         description="Filter by published status (1 for published, 0 for unpublished)",
     *         required=false,
     *         @OA\Schema(type="integer", enum={0, 1})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of accommodations",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="data", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beach House"),
     *                 @OA\Property(property="description", type="string", example="Beautiful beach house with ocean view"),
     *                 @OA\Property(property="address", type="string", example="123 Beach Road"),
     *                 @OA\Property(property="city", type="string", example="Cape Town"),
     *                 @OA\Property(property="province", type="string", example="Western Cape"),
     *                 @OA\Property(property="post_code", type="string", example="8001"),
     *                 @OA\Property(property="country", type="string", example="South Africa"),
     *                 @OA\Property(property="min_occupancy", type="integer", example=2),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4),
     *                 @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *                 @OA\Property(property="published", type="boolean", example=true),
     *                 @OA\Property(property="default_price", type="string", example="100.00"),
     *                 @OA\Property(property="gallery_images", type="array", @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="beach-house-1"),
     *                     @OA\Property(property="file_name", type="string", example="beach-house-1.jpg"),
     *                     @OA\Property(property="mime_type", type="string", example="image/jpeg"),
     *                     @OA\Property(property="original_url", type="string", example="https://example.com/storage/1/beach-house-1.jpg"),
     *                     @OA\Property(property="thumbnail", type="string", example="https://example.com/storage/1/conversions/beach-house-1-thumb.jpg"),
     *                     @OA\Property(property="medium", type="string", example="https://example.com/storage/1/conversions/beach-house-1-medium.jpg"),
     *                     @OA\Property(property="large", type="string", example="https://example.com/storage/1/conversions/beach-house-1-large.jpg")
     *                 )),
     *                 @OA\Property(property="group", type="object", nullable=true,
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Beach Properties"),
     *                     @OA\Property(property="description", type="string", example="Properties near the beach")
     *                 )
     *             )),
     *             @OA\Property(property="meta", type="object",
     *                 @OA\Property(property="total", type="integer", example=10),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="last_page", type="integer", example=1)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        // For authenticated user requests
		$user = Auth::user();
		$teamIds = $user->allTeams()->pluck('id')->toArray();

		$query = Accommodation::where(function ($query) use ($user, $teamIds) {
				$query->where('user_id', $user->id);

				if (!empty($teamIds)) {
					$query->orWhereIn('team_id', $teamIds);
				}
			})
			->with(['group']);

        // Filter by published status if provided
        if ($request->has('published')) {
            $query->where('published', filter_var($request->published, FILTER_VALIDATE_BOOLEAN));
        }

        // Filter by group if provided
        if ($request->has('group_id')) {
            $query->where('accommodation_group_id', $request->group_id);
        }

        $accommodations = $query->paginate(15);

        return response()->json($accommodations);
    }

    /**
     * Get a specific accommodation by ID.
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations/{id}",
     *     summary="Get accommodation details",
     *     description="Returns details of a specific accommodation",
     *     operationId="getAccommodation",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Accommodation ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Accommodation details",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="Beach House"),
     *             @OA\Property(property="description", type="string", example="Beautiful beach house with ocean view"),
     *             @OA\Property(property="address", type="string", example="123 Beach Road"),
     *             @OA\Property(property="city", type="string", example="Cape Town"),
     *             @OA\Property(property="province", type="string", example="Western Cape"),
     *             @OA\Property(property="post_code", type="string", example="8001"),
     *             @OA\Property(property="country", type="string", example="South Africa"),
     *             @OA\Property(property="min_occupancy", type="integer", example=2),
     *             @OA\Property(property="max_occupancy", type="integer", example=4),
     *             @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *             @OA\Property(property="published", type="boolean", example=true),
     *             @OA\Property(property="default_price", type="string", example="100.00"),
     *             @OA\Property(property="gallery_images", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="beach-house-1"),
     *                 @OA\Property(property="file_name", type="string", example="beach-house-1.jpg"),
     *                 @OA\Property(property="mime_type", type="string", example="image/jpeg"),
     *                 @OA\Property(property="original_url", type="string", example="https://example.com/storage/1/beach-house-1.jpg"),
     *                 @OA\Property(property="thumbnail", type="string", example="https://example.com/storage/1/conversions/beach-house-1-thumb.jpg"),
     *                 @OA\Property(property="medium", type="string", example="https://example.com/storage/1/conversions/beach-house-1-medium.jpg"),
     *                 @OA\Property(property="large", type="string", example="https://example.com/storage/1/conversions/beach-house-1-large.jpg")
     *             )),
     *             @OA\Property(property="group", type="object", nullable=true,
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beach Properties"),
     *                 @OA\Property(property="description", type="string", example="Properties near the beach")
     *             ),
     *             @OA\Property(property="prices", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="price", type="number", format="float", example=100.00),
     *                 @OA\Property(property="additional_person_price", type="number", format="float", example=25.00),
     *                 @OA\Property(property="type", type="string", example="default"),
     *                 @OA\Property(property="start_date", type="string", format="date", nullable=true),
     *                 @OA\Property(property="end_date", type="string", format="date", nullable=true),
     *                 @OA\Property(property="day_of_week", type="integer", nullable=true, example=0),
     *                 @OA\Property(property="priority", type="integer", example=1)
     *             ))
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to access it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function show($id, Request $request)
    {
        // Check if this is a widget request (has authenticated_site attribute)
        $isWidgetRequest = $request->attributes->has('authenticated_site');

        if ($isWidgetRequest) {
            // For widget requests, we only show published accommodations
            $accommodation = Accommodation::where('id', $id)
                ->where('published', true)
                ->with(['group', 'prices'])
                ->first();
        } else {
            // For authenticated user requests
            $user = Auth::user();
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            $accommodation = Accommodation::where('id', $id)
                ->where(function ($query) use ($user, $teamIds) {
                    $query->where('user_id', $user->id);

                    if (!empty($teamIds)) {
                        $query->orWhereIn('team_id', $teamIds);
                    }
                })
                ->with(['media', 'group', 'prices'])
                ->first();
        }

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found or you do not have permission to access it'], 404);
        }

        // Load additional relationships
        $accommodation->load(['discounts', 'group.discounts']);
        
        // Add final price information to the response
        $responseData = $accommodation->toArray();
        
        // Add sample final price for the next 7 days
        $samplePrices = [];
        $today = now()->format('Y-m-d');
        
        for ($i = 0; $i < 7; $i++) {
            $date = now()->addDays($i)->format('Y-m-d');
            $samplePrices[$date] = $accommodation->getFinalPrice($date, $accommodation->min_occupancy);
        }
        
        $responseData['sample_prices'] = $samplePrices;

        return response()->json(['data' => $responseData]);
    }

    /**
     * Check the availability of the specified accommodation for given dates.
     *
     * @param string $id The ID of the accommodation to check
     * @param Request $request The request containing start_date, end_date, and number_of_persons
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Post(
     *     path="/api/accommodations/{id}/check-availability",
     *     summary="Check accommodation availability for specific dates",
     *     description="Returns availability status, pricing information, and any conflicts for the specified dates",
     *     operationId="checkAccommodationAvailabilityPost",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Accommodation ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Availability check parameters",
     *         @OA\JsonContent(
     *             required={"start_date", "end_date"},
     *             @OA\Property(property="start_date", type="string", format="date", example="2023-06-01", description="Start date (format: Y-m-d)"),
     *             @OA\Property(property="end_date", type="string", format="date", example="2023-06-05", description="End date (format: Y-m-d)"),
     *             @OA\Property(property="number_of_persons", type="integer", example=2, description="Number of persons (defaults to minimum occupancy)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Availability information",
     *         @OA\JsonContent(
     *             @OA\Property(property="available", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Accommodation is available for the specified dates"),
     *             @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *             @OA\Property(
     *                 property="pricing",
     *                 type="object",
     *                 @OA\Property(property="daily_prices", type="object", example={"2023-06-01": 250, "2023-06-02": 250, "2023-06-03": 300, "2023-06-04": 300, "2023-06-05": 150}),
     *                 @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *                 @OA\Property(property="currency", type="string", example="ZAR"),
     *                 @OA\Property(property="occupancy", type="integer", example=2),
     *                 @OA\Property(property="min_occupancy", type="integer", example=1),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid input",
     *         @OA\JsonContent(
     *             @OA\Property(property="start_date", type="array", @OA\Items(type="string"), example={"The start date field is required."}),
     *             @OA\Property(property="end_date", type="array", @OA\Items(type="string"), example={"The end date must be a date after start date."})
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found")
     *         )
     *     )
     * )
     */
    public function checkAvailability(string $id, Request $request) 
    {
        try {
            // Initialize request variables first
            $start_date = $request->input('start_date');
            $end_date = $request->input('end_date');
            $requestedOccupancy = $request->input('number_of_persons', $request->input('occupancy'));

            // Validate dates
            if (!$start_date || !$end_date) {
                return response()->json([
                    'message' => 'The start_date and end_date are required.',
                    'errors' => [
                        'start_date' => ['The start date field is required.'],
                        'end_date' => ['The end date field is required.']
                    ]
                ], 400);
            }

            try {
                $startDateTime = \Carbon\Carbon::parse($start_date);
                $endDateTime = \Carbon\Carbon::parse($end_date);

                if ($endDateTime->lessThanOrEqualTo($startDateTime)) {
                    return response()->json([
                        'message' => 'The end date must be after the start date.',
                        'errors' => [
                            'end_date' => ['The end date must be after the start date.']
                        ]
                    ], 400);
                }

                if ($startDateTime->lessThan(now()->startOfDay())) {
                    return response()->json([
                        'message' => 'The start date must not be in the past.',
                        'errors' => [
                            'start_date' => ['The start date must not be in the past.']
                        ]
                    ], 400);
                }
            } catch (\Exception $e) {
                return response()->json([
                    'message' => 'Invalid date format.',
                    'errors' => [
                        'dates' => ['The dates must be in a valid format (YYYY-MM-DD).']
                    ]
                ], 400);
            }

            Log::info('Starting availability check', [
                'accommodation_id' => $id,
                'request_data' => [
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'number_of_persons' => $requestedOccupancy
                ],
                'user' => Auth::check() ? Auth::id() : 'unauthenticated'
            ]);

            // Check if this is a widget request (has authenticated_site attribute)
            $isWidgetRequest = $request->attributes->has('authenticated_site');
            Log::info('Request type', ['isWidgetRequest' => $isWidgetRequest]);

            if ($isWidgetRequest) {
                // For widget requests, we only check published accommodations
                $accommodation = Accommodation::published()
                    ->with('group') // Load the group relationship for pricing fallback
                    ->where('id', $id)
                    ->first();
            } else {
                Log::info('Looking up accommodation for authenticated user');
                // For authenticated user requests
                $user = Auth::user();
                $teamIds = $user->allTeams()->pluck('id')->toArray();
                Log::info('User team IDs', ['teamIds' => $teamIds]);

                // Find accommodation and ensure it belongs to the authenticated user or their team and is published
                $accommodation = Accommodation::published()
                    ->with('group') // Load the group relationship for pricing fallback
                    ->where('id', $id)
                    ->where(function ($query) use ($user, $teamIds) {
                        $query->where('user_id', $user->id);

                        if (!empty($teamIds)) {
                            $query->orWhereIn('team_id', $teamIds);
                        }
                    })
                    ->first();
            }

            if (!$accommodation) {
                return response()->json(['message' => 'Accommodation not found or you do not have permission to access it'], 404);
            }

            Log::info('Found accommodation', [
                'id' => $accommodation->id,
                'name' => $accommodation->name
            ]);

            // Check minimum stay requirement
            $nights = \Carbon\Carbon::parse($start_date)->diffInDays(\Carbon\Carbon::parse($end_date));
            Log::info('Checking minimum stay', [
                'minimum_stay' => $accommodation->minimum_stay ?? 1,
                'requested_nights' => $nights
            ]);

            if (isset($accommodation->minimum_stay) && $nights < $accommodation->minimum_stay) {
                return response()->json([
                    'available' => false,
                    'message' => "Minimum stay is {$accommodation->minimum_stay} nights",
                    'reason' => 'minimum_stay',
                    'details' => [
                        'minimum_stay' => $accommodation->minimum_stay,
                        'nights' => $nights
                    ]
                ]);
            }

            // Check for bookings
            $existingBookings = \App\Models\Booking::where('accommodation_id', $accommodation->id)
                ->where(function ($query) use ($start_date, $end_date) {
                    $query->whereBetween('start_date', [$start_date, $end_date])
                        ->orWhereBetween('end_date', [$start_date, $end_date])
                        ->orWhere(function ($query) use ($start_date, $end_date) {
                            $query->where('start_date', '<=', $start_date)
                                ->where('end_date', '>=', $end_date);
                        });
                })->count();

            if ($existingBookings > 0) {
                return response()->json([
                    'available' => false,
                    'message' => 'Accommodation is not available for the specified dates',
                    'reason' => 'existing_booking'
                ]);
            }

            // Get occupancy from request or use minimum occupancy
            $occupancy = $requestedOccupancy ?? $accommodation->min_occupancy;

            // Calculate total price
            $totalPrice = 0;
            $currentDate = \Carbon\Carbon::parse($start_date);
            $endDate = \Carbon\Carbon::parse($end_date);
            $dailyPrices = [];

            while ($currentDate < $endDate) {
                $dateStr = $currentDate->format('Y-m-d');
                $priceInfo = $accommodation->getFinalPrice($dateStr, $occupancy);
                $dailyPrices[$dateStr] = $priceInfo['price'];
                $totalPrice += $priceInfo['price'];
                $currentDate->addDay();
            }

            return response()->json([
                'available' => true,
                'message' => 'Accommodation is available for the specified dates',
                'total_price' => $totalPrice,
                'pricing' => [
                    'daily_prices' => $dailyPrices,
                    'total_price' => $totalPrice,
                    'currency' => 'R',
                    'occupancy' => $occupancy,
                    'min_occupancy' => $accommodation->min_occupancy,
                    'max_occupancy' => $accommodation->max_occupancy
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Availability check failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'accommodation_id' => $id,
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'message' => 'An error occurred while checking availability',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check the availability of the specified accommodation for given dates (GET method for backward compatibility).
     *
     * @param string $id The ID of the accommodation to check
     * @param Request $request The request containing start_date, end_date, and occupancy
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations/{id}/check-availability",
     *     summary="[DEPRECATED] Check accommodation availability for specific dates",
     *     description="Returns availability status, pricing information, and any conflicts for the specified dates. This GET endpoint is maintained for backward compatibility and will be removed in a future version. Please use the POST endpoint instead.",
     *     operationId="checkAccommodationAvailabilityGet",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Accommodation ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         required=true,
     *         description="Start date (format: Y-m-d)",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         required=true,
     *         description="End date (format: Y-m-d)",
     *         @OA\Schema(type="string", format="date")
     *     ),
     *     @OA\Parameter(
     *         name="occupancy",
     *         in="query",
     *         required=false,
     *         description="Number of occupants (defaults to minimum occupancy)",
     *         @OA\Schema(type="integer", minimum=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Availability information",
     *         @OA\JsonContent(
     *             @OA\Property(property="available", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Accommodation is available for the specified dates"),
     *             @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *             @OA\Property(
     *                 property="pricing",
     *                 type="object",
     *                 @OA\Property(property="daily_prices", type="object", example={"2023-06-01": 250, "2023-06-02": 250, "2023-06-03": 300, "2023-06-04": 300, "2023-06-05": 150}),
     *                 @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *                 @OA\Property(property="currency", type="string", example="ZAR"),
     *                 @OA\Property(property="occupancy", type="integer", example=2),
     *                 @OA\Property(property="min_occupancy", type="integer", example=1),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid input",
     *         @OA\JsonContent(
     *             @OA\Property(property="start_date", type="array", @OA\Items(type="string"), example={"The start date field is required."}),
     *             @OA\Property(property="end_date", type="array", @OA\Items(type="string"), example={"The end date must be a date after start date."})
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found")
     *         )
     *     )
     * )
     */
    public function checkAvailabilityGet(string $id, Request $request)
    {
        // Forward the GET request to the main checkAvailability method
        return $this->checkAvailability($id, $request);
    }

    /**
     * Get all unavailable dates for an accommodation.
     *
     * @param string $id The ID of the accommodation
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations/{id}/unavailable-dates",
     *     summary="Get unavailable dates for an accommodation",
     *     description="Returns a list of all unavailable dates for the specified accommodation",
     *     operationId="getAccommodationUnavailableDates",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Accommodation ID",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of unavailable dates",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="type", type="string", example="date_range"),
     *                 @OA\Property(property="start_date", type="string", format="date", example="2023-06-01"),
     *                 @OA\Property(property="end_date", type="string", format="date", example="2023-06-05"),
     *                 @OA\Property(property="days_of_week", type="array", @OA\Items(type="integer"), example={0, 6}),
     *                 @OA\Property(property="reason", type="string", example="Maintenance"),
     *                 @OA\Property(property="active", type="boolean", example=true)
     *             ))
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to access it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function getUnavailableDates(string $id, Request $request)
    {
        // Check if this is a widget request (has authenticated_site attribute)
        $isWidgetRequest = $request->attributes->has('authenticated_site');

        if ($isWidgetRequest) {
            // For widget requests, we only check published accommodations
            $accommodation = Accommodation::published()
                ->where('id', $id)
                ->first();
        } else {
            // For authenticated user requests
            $user = Auth::user();
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            // Find accommodation and ensure it belongs to the authenticated user or their team and is published
            $accommodation = Accommodation::published()
                ->where('id', $id)
                ->where(function ($query) use ($user, $teamIds) {
                    $query->where('user_id', $user->id);

                    if (!empty($teamIds)) {
                        $query->orWhereIn('team_id', $teamIds);
                    }
                })
                ->first();
        }

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found or you do not have permission to access it'], 404);
        }

        // Get all active unavailable periods for this accommodation
        $unavailablePeriods = \App\Models\AccommodationUnavailablePeriod::where('accommodation_id', $id)
            ->where('active', true)
            ->orderBy('start_date')
            ->get();

        return response()->json([
            'data' => $unavailablePeriods
        ]);
    }

    /**
     * Get all available discounts for an accommodation.
     *
     * @param string $id The ID of the accommodation
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations/{id}/discounts",
     *     summary="Get all available discounts for an accommodation",
     *     description="Returns all active discounts available for the specified accommodation with their conditions and requirements",
     *     operationId="getAccommodationDiscounts",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="The ID of the accommodation",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="List of available discounts",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="array", @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Early Bird Discount"),
     *                 @OA\Property(property="percentage", type="number", format="float", example=15.00),
     *                 @OA\Property(property="rule_type", type="string", example="advance_booking"),
     *                 @OA\Property(property="description", type="string", example="Book 30 days in advance and save 15%"),
     *                 @OA\Property(property="conditions", type="object",
     *                     @OA\Property(property="min_days_in_advance", type="integer", example=30),
     *                     @OA\Property(property="min_length_of_stay", type="integer", example=null),
     *                     @OA\Property(property="period_start_date", type="string", format="date", example=null),
     *                     @OA\Property(property="period_end_date", type="string", format="date", example=null)
     *                 )
     *             ))
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found")
     *         )
     *     )
     * )
     */
    public function getDiscounts($id)
    {
        // Find the accommodation (only published ones for widget access) with discounts and group discounts
        $accommodation = Accommodation::with(['discounts', 'group.discounts'])
            ->where('id', $id)
            ->where('published', true)
            ->first();

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found'], 404);
        }

        // Get all active discounts for this accommodation and its group
        $discounts = $accommodation->discounts
            ->merge($accommodation->group ? $accommodation->group->discounts : collect())
            ->where('is_active', true)
            ->sortBy('priority')
            ->values();

        // Transform discounts to include human-readable descriptions
        $transformedDiscounts = $discounts->map(function ($discount) {
            return [
                'id' => $discount->id,
                'name' => $discount->name,
                'percentage' => $discount->percentage,
                'rule_type' => $discount->rule_type,
                'description' => $this->generateDiscountDescription($discount),
                'conditions' => [
                    'min_days_in_advance' => $discount->min_days_in_advance,
                    'min_length_of_stay' => $discount->min_length_of_stay,
                    'period_start_date' => $discount->period_start_date,
                    'period_end_date' => $discount->period_end_date,
                ]
            ];
        });

        return response()->json([
            'data' => $transformedDiscounts
        ]);
    }

    /**
     * Generate a human-readable description for a discount based on its rules.
     *
     * @param \App\Models\Discount $discount
     * @return string
     */
    private function generateDiscountDescription($discount)
    {
        $description = "Save {$discount->percentage}% ";

        switch ($discount->rule_type) {
            case 'advance_booking':
                if ($discount->min_days_in_advance) {
                    $description .= "when you book at least {$discount->min_days_in_advance} days in advance";
                }
                break;

            case 'length_of_stay':
                if ($discount->min_length_of_stay) {
                    $days = $discount->min_length_of_stay == 1 ? 'day' : 'days';
                    $description .= "for stays of {$discount->min_length_of_stay} {$days} or longer";
                }
                break;

            case 'time_period':
                if ($discount->period_start_date && $discount->period_end_date) {
                    $startDate = \Carbon\Carbon::parse($discount->period_start_date)->format('M j, Y');
                    $endDate = \Carbon\Carbon::parse($discount->period_end_date)->format('M j, Y');
                    $description .= "for bookings made between {$startDate} and {$endDate}";
                }
                break;

            default:
                $description .= "on your booking";
                break;
        }

        return $description;
    }

    /**
     * Get comprehensive unavailability data for an accommodation.
     * This includes unavailable periods, existing bookings, minimum stay, and minimum booking notice.
     *
     * @param string $id The ID of the accommodation
     * @param Request $request The request object
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Get(
     *     path="/api/accommodations/{id}/unavailability-data",
     *     summary="Get comprehensive unavailability data for an accommodation",
     *     description="Returns comprehensive unavailability data including unavailable periods, existing bookings, minimum stay requirements, and minimum booking notice periods. This endpoint is designed for calendar widgets that need complete unavailability information.",
     *     operationId="getAccommodationUnavailabilityData",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="The ID of the accommodation",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         required=false,
     *         description="Start date to filter results (Y-m-d format)",
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         required=false,
     *         description="End date to filter results (Y-m-d format)",
     *         @OA\Schema(type="string", format="date", example="2024-12-31")
     *     ),
     *     @OA\Parameter(
     *         name="format",
     *         in="query",
     *         required=false,
     *         description="Response format: 'detailed' (default) or 'dates_array' for simple date list",
     *         @OA\Schema(type="string", enum={"detailed", "dates_array"}, example="detailed")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful response",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="accommodation_id", type="integer", example=1),
     *                 @OA\Property(property="minimum_stay", type="integer", example=2),
     *                 @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *                 @OA\Property(property="minimum_booking_notice_cutoff", type="string", format="date", example="2024-01-02", nullable=true),
     *                 @OA\Property(property="unavailable_periods", type="array", @OA\Items(type="object")),
     *                 @OA\Property(property="existing_bookings", type="array", @OA\Items(type="object"))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to access it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function getUnavailabilityData(string $id, Request $request)
    {
        // Check if this is a widget request (has authenticated_site attribute)
        $isWidgetRequest = $request->attributes->has('authenticated_site');

        if ($isWidgetRequest) {
            // For widget requests, we only check published accommodations
            $accommodation = Accommodation::published()
                ->where('id', $id)
                ->first();
        } else {
            // For authenticated user requests
            $user = Auth::user();
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            // Find accommodation and ensure it belongs to the authenticated user or their team and is published
            $accommodation = Accommodation::published()
                ->where('id', $id)
                ->where(function ($query) use ($user, $teamIds) {
                    $query->where('user_id', $user->id);

                    if (!empty($teamIds)) {
                        $query->orWhereIn('team_id', $teamIds);
                    }
                })
                ->first();
        }

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found or you do not have permission to access it'], 404);
        }

        $unavailabilityService = app(\App\Services\AccommodationUnavailabilityService::class);

        $startDate = $request->query('start_date');
        $endDate = $request->query('end_date');
        $format = $request->query('format', 'detailed');

        if ($format === 'dates_array') {
            // Return simple array of unavailable dates for calendar widgets
            $unavailableDates = $unavailabilityService->getUnavailableDatesArray($accommodation, $startDate, $endDate);

            return response()->json([
                'data' => [
                    'accommodation_id' => $accommodation->id,
                    'unavailable_dates' => $unavailableDates
                ]
            ]);
        } else {
            // Return detailed unavailability data
            $unavailabilityData = $unavailabilityService->getUnavailabilityData($accommodation, $startDate, $endDate);

            return response()->json([
                'data' => $unavailabilityData
            ]);
        }
    }

    /**
     * Store a newly created accommodation.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Post(
     *     path="/api/accommodations",
     *     summary="Create a new accommodation",
     *     description="Creates a new accommodation for the authenticated user",
     *     operationId="storeAccommodation",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Accommodation data",
     *         @OA\JsonContent(
     *             required={"name", "description", "address", "city", "country", "price"},
     *             @OA\Property(property="name", type="string", example="Beach House"),
     *             @OA\Property(property="description", type="string", example="Beautiful beach house with ocean view"),
     *             @OA\Property(property="address", type="string", example="123 Beach Road"),
     *             @OA\Property(property="city", type="string", example="Cape Town"),
     *             @OA\Property(property="province", type="string", example="Western Cape"),
     *             @OA\Property(property="post_code", type="string", example="8001"),
     *             @OA\Property(property="country", type="string", example="South Africa"),
     *             @OA\Property(property="min_occupancy", type="integer", example=2),
     *             @OA\Property(property="max_occupancy", type="integer", example=4),
     *             @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *             @OA\Property(property="price", type="number", format="float", example="100.00"),
     *             @OA\Property(property="additional_person_price", type="number", format="float", example="25.00"),
     *             @OA\Property(property="accommodation_group_id", type="integer", nullable=false, example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Accommodation created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beach House"),
     *                 @OA\Property(property="description", type="string", example="Beautiful beach house with ocean view"),
     *                 @OA\Property(property="address", type="string", example="123 Beach Road"),
     *                 @OA\Property(property="city", type="string", example="Cape Town"),
     *                 @OA\Property(property="province", type="string", example="Western Cape"),
     *                 @OA\Property(property="post_code", type="string", example="8001"),
     *                 @OA\Property(property="country", type="string", example="South Africa"),
     *                 @OA\Property(property="min_occupancy", type="integer", example=2),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4),
     *                 @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *                 @OA\Property(property="published", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object",
     *                 @OA\Property(property="name", type="array", @OA\Items(type="string", example="The name field is required.")),
     *                 @OA\Property(property="description", type="array", @OA\Items(type="string", example="The description field is required."))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'province' => 'nullable|string|max:255',
            'post_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:255',
            'min_occupancy' => 'nullable|integer|min:1',
            'max_occupancy' => 'nullable|integer|min:1|gte:min_occupancy',
            'minimum_booking_notice' => 'nullable|integer|min:0',
            'price' => 'required|numeric|min:0',
            'additional_person_price' => 'nullable|numeric|min:0',
            'accommodation_group_id' => 'required|integer|exists:accommodation_groups,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if the accommodation group belongs to the user or their team
        if ($request->has('accommodation_group_id') && $request->accommodation_group_id) {
            $teamIds = $user->allTeams()->pluck('id')->toArray();
            $group = \App\Models\AccommodationGroup::find($request->accommodation_group_id);
            
            if (!$group) {
                return response()->json(['message' => 'The selected accommodation group does not exist.'], 404);
            }
            
            // Check if group belongs to user or their team
            if ($group->user_id !== $user->id && !in_array($group->team_id, $teamIds)) {
                return response()->json(['message' => 'The selected accommodation group does not belong to you or your team.'], 403);
            }
        }

        // Create the accommodation
        $accommodation = new \App\Models\Accommodation([
            'name' => $request->name,
            'description' => $request->description,
            'address' => $request->address,
            'city' => $request->city,
            'province' => $request->province,
            'post_code' => $request->post_code,
            'country' => $request->country,
            'min_occupancy' => $request->min_occupancy ?? 1,
            'max_occupancy' => $request->max_occupancy ?? 2,
            'minimum_booking_notice' => $request->minimum_booking_notice ?? 0,
            'published' => false, // Default to unpublished
            'accommodation_group_id' => $request->accommodation_group_id
        ]);

        $accommodation->user_id = $user->id;
        $accommodation->team_id = $user->current_team_id;
        $accommodation->save();

        // Create default price
        \App\Models\AccommodationPrice::create([
            'accommodation_id' => $accommodation->id,
            'price' => $request->price,
            'additional_person_price' => $request->additional_person_price ?? 0,
            'type' => 'default',
            'priority' => 1
        ]);

        return response()->json(['data' => $accommodation], 201);
    }

    /**
     * Update the specified accommodation.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Put(
     *     path="/api/accommodations/{id}",
     *     summary="Update an accommodation",
     *     description="Updates an existing accommodation belonging to the authenticated user",
     *     operationId="updateAccommodation",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Accommodation ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Accommodation data",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Updated Beach House"),
     *             @OA\Property(property="description", type="string", example="Updated description"),
     *             @OA\Property(property="address", type="string", example="123 Beach Road"),
     *             @OA\Property(property="city", type="string", example="Cape Town"),
     *             @OA\Property(property="province", type="string", example="Western Cape"),
     *             @OA\Property(property="post_code", type="string", example="8001"),
     *             @OA\Property(property="country", type="string", example="South Africa"),
     *             @OA\Property(property="min_occupancy", type="integer", example=2),
     *             @OA\Property(property="max_occupancy", type="integer", example=4),
     *             @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *             @OA\Property(property="price", type="number", format="float", example=150.00),
     *             @OA\Property(property="additional_person_price", type="number", format="float", example=35.00),
     *             @OA\Property(property="accommodation_group_id", type="integer", nullable=false, example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Accommodation updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Updated Beach House"),
     *                 @OA\Property(property="description", type="string", example="Updated description"),
     *                 @OA\Property(property="address", type="string", example="123 Beach Road"),
     *                 @OA\Property(property="city", type="string", example="Cape Town"),
     *                 @OA\Property(property="province", type="string", example="Western Cape"),
     *                 @OA\Property(property="post_code", type="string", example="8001"),
     *                 @OA\Property(property="country", type="string", example="South Africa"),
     *                 @OA\Property(property="min_occupancy", type="integer", example=2),
     *                 @OA\Property(property="max_occupancy", type="integer", example=4),
     *                 @OA\Property(property="minimum_booking_notice", type="integer", example=1),
     *                 @OA\Property(property="published", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to update it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="You do not have permission to update this accommodation")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object",
     *                 @OA\Property(property="name", type="array", @OA\Items(type="string", example="The name field is required.")),
     *                 @OA\Property(property="description", type="array", @OA\Items(type="string", example="The description field is required."))
     *             )
     *         )
     *     )
     * )
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // First check if the accommodation exists at all
        $accommodation = \App\Models\Accommodation::find($id);

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found'], 404);
        }

        // Then check if it belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            return response()->json(['message' => 'You do not have permission to update this accommodation'], 403);
        }

        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'address' => 'sometimes|required|string|max:255',
            'city' => 'sometimes|required|string|max:255',
            'province' => 'sometimes|nullable|string|max:255',
            'post_code' => 'sometimes|nullable|string|max:20',
            'country' => 'sometimes|required|string|max:255',
            'min_occupancy' => 'sometimes|integer|min:1',
            'max_occupancy' => 'sometimes|integer|min:1|gte:min_occupancy',
            'minimum_booking_notice' => 'sometimes|integer|min:0',
            'price' => 'sometimes|required|numeric|min:0',
            'additional_person_price' => 'sometimes|nullable|numeric|min:0',
            'accommodation_group_id' => 'sometimes|integer|exists:accommodation_groups,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if the accommodation group belongs to the user or their team
        if ($request->has('accommodation_group_id') && $request->accommodation_group_id) {
            $group = \App\Models\AccommodationGroup::find($request->accommodation_group_id);
            
            if (!$group) {
                return response()->json(['message' => 'The selected accommodation group does not exist.'], 404);
            }
            
            // Check if group belongs to user or their team
            if ($group->user_id !== $user->id && !in_array($group->team_id, $teamIds)) {
                return response()->json(['message' => 'The selected accommodation group does not belong to you or your team.'], 403);
            }
        }

        // Update the accommodation
        $accommodation->fill($request->only([
            'name',
            'description',
            'address',
            'city',
            'province',
            'post_code',
            'country',
            'min_occupancy',
            'max_occupancy',
            'minimum_booking_notice',
            'accommodation_group_id'
        ]));

        $accommodation->save();

        // Update default price if provided
        if ($request->has('price')) {
            $defaultPrice = \App\Models\AccommodationPrice::firstOrNew([
                'accommodation_id' => $accommodation->id,
                'type' => 'default'
            ]);

            $defaultPrice->price = $request->price;

            if ($request->has('additional_person_price')) {
                $defaultPrice->additional_person_price = $request->additional_person_price;
            }

            $defaultPrice->priority = 1;
            $defaultPrice->save();
        }

        return response()->json(['data' => $accommodation]);
    }

    /**
     * Remove the specified accommodation.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Delete(
     *     path="/api/accommodations/{id}",
     *     summary="Delete an accommodation",
     *     description="Deletes an existing accommodation belonging to the authenticated user",
     *     operationId="deleteAccommodation",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Accommodation ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Accommodation deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to delete it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="You do not have permission to delete this accommodation")
     *         )
     *     )
     * )
     */
    public function destroy($id)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // First check if the accommodation exists at all
        $accommodation = \App\Models\Accommodation::find($id);

        if (!$accommodation) {
            return response()->json(['message' => 'Accommodation not found'], 404);
        }

        // Then check if it belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            return response()->json(['message' => 'You do not have permission to delete this accommodation'], 403);
        }

        // Soft delete the accommodation
        $accommodation->delete();

        return response()->json(['message' => 'Accommodation deleted successfully']);
    }

    /**
     * Check availability across all accommodations in an accommodation group.
     *
     * @param string $groupId The ID of the accommodation group
     * @param Request $request The request containing start_date, end_date, and number_of_persons
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Post(
     *     path="/api/accommodation-groups/{groupId}/check-availability",
     *     summary="Check availability across all accommodations in a group",
     *     description="Returns availability status for all accommodations in the specified group for given dates",
     *     operationId="checkAccommodationGroupAvailability",
     *     tags={"Accommodations"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\Parameter(
     *         name="groupId",
     *         in="path",
     *         required=true,
     *         description="The ID of the accommodation group",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"start_date", "end_date"},
     *             @OA\Property(property="start_date", type="string", format="date", example="2024-07-15", description="Check-in date (Y-m-d format)"),
     *             @OA\Property(property="end_date", type="string", format="date", example="2024-07-20", description="Check-out date (Y-m-d format)"),
     *             @OA\Property(property="number_of_persons", type="integer", example=2, description="Number of persons (optional, defaults to minimum occupancy)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Availability check completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="group_id", type="integer", example=1),
     *             @OA\Property(property="group_name", type="string", example="Beach House Collection"),
     *             @OA\Property(property="has_availability", type="boolean", example=true, description="True if at least one accommodation is available"),
     *             @OA\Property(property="total_accommodations", type="integer", example=5),
     *             @OA\Property(property="available_accommodations", type="integer", example=3),
     *             @OA\Property(
     *                 property="accommodations",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Ocean View Villa"),
     *                     @OA\Property(property="available", type="boolean", example=true),
     *                     @OA\Property(property="total_price", type="string", example="1500.00"),
     *                     @OA\Property(property="reason", type="string", example="available"),
     *                     @OA\Property(property="message", type="string", example="Accommodation is available for the specified dates")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation group not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation group not found or you do not have permission to access it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function checkGroupAvailability(string $groupId, Request $request)
    {
        // Validate request data
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'number_of_persons' => 'nullable|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if this is a widget request (has authenticated_site attribute)
        $isWidgetRequest = $request->attributes->has('authenticated_site');

        // Find the accommodation group
        if ($isWidgetRequest) {
            // For widget requests, we only show published groups
            $group = AccommodationGroup::where('id', $groupId)
                ->where('published', true)
                ->with(['accommodations' => function ($query) {
                    $query->where('published', true);
                }])
                ->first();
        } else {
            // For authenticated user requests
            $user = Auth::user();
            $teamIds = $user->allTeams()->pluck('id')->toArray();

            $group = AccommodationGroup::where('id', $groupId)
                ->where(function ($query) use ($user, $teamIds) {
                    $query->where('user_id', $user->id);

                    if (!empty($teamIds)) {
                        $query->orWhereIn('team_id', $teamIds);
                    }
                })
                ->with('accommodations')
                ->first();
        }

        if (!$group) {
            return response()->json(['message' => 'Accommodation group not found or you do not have permission to access it'], 404);
        }

        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $requestedOccupancy = $request->number_of_persons;

        $accommodationResults = [];
        $availableCount = 0;
        $totalCount = $group->accommodations->count();

        // Check availability for each accommodation in the group
        foreach ($group->accommodations as $accommodation) {
            $result = $this->checkSingleAccommodationAvailability($accommodation, $startDate, $endDate, $requestedOccupancy);

            $accommodationResults[] = [
                'id' => $accommodation->id,
                'name' => $accommodation->name,
                'available' => $result['available'],
                'total_price' => $result['available'] ? $result['total_price'] : null,
                'reason' => $result['reason'],
                'message' => $result['message'],
                'details' => $result['details'] ?? null
            ];

            if ($result['available']) {
                $availableCount++;
            }
        }

        return response()->json([
            'group_id' => (int) $groupId,
            'group_name' => $group->name,
            'has_availability' => $availableCount > 0,
            'total_accommodations' => $totalCount,
            'available_accommodations' => $availableCount,
            'accommodations' => $accommodationResults
        ]);
    }

    /**
     * Helper method to check availability for a single accommodation.
     * This extracts the core availability logic to be reused by both individual and group checks.
     *
     * @param Accommodation $accommodation
     * @param string $startDate
     * @param string $endDate
     * @param int|null $requestedOccupancy
     * @return array
     */
    private function checkSingleAccommodationAvailability(Accommodation $accommodation, string $startDate, string $endDate, ?int $requestedOccupancy = null): array
    {
        // Check minimum booking notice
        if ($accommodation->minimum_booking_notice > 0) {
            $today = now()->startOfDay();
            $bookingStartDate = \Carbon\Carbon::parse($startDate)->startOfDay();
            $daysUntilBooking = $today->diffInDays($bookingStartDate);

            Log::info('Checking minimum booking notice', [
                'minimum_booking_notice' => $accommodation->minimum_booking_notice,
                'days_until_booking' => $daysUntilBooking
            ]);

            if ($daysUntilBooking < $accommodation->minimum_booking_notice) {
                return [
                    'available' => false,
                    'message' => "Booking must be made at least {$accommodation->minimum_booking_notice} days in advance",
                    'reason' => 'minimum_notice',
                    'details' => [
                        'minimum_booking_notice' => $accommodation->minimum_booking_notice,
                        'days_until_booking' => $daysUntilBooking
                    ]
                ];
            }
        }

        // Check minimum stay requirement
        $nights = \Carbon\Carbon::parse($startDate)->diffInDays(\Carbon\Carbon::parse($endDate));
        Log::info('Checking minimum stay requirement', [
            'minimum_stay' => $accommodation->minimum_stay,
            'requested_nights' => $nights
        ]);

        if ($nights < $accommodation->minimum_stay) {
            return [
                'available' => false,
                'message' => "Minimum stay is {$accommodation->minimum_stay} nights",
                'reason' => 'minimum_stay',
                'details' => [
                    'minimum_stay' => $accommodation->minimum_stay,
                    'nights' => $nights
                ]
            ];
        }

        // Check if there are any bookings
        $bookings = \App\Models\Booking::where('accommodation_id', $accommodation->id)
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate])
                    ->orWhere(function ($query) use ($startDate, $endDate) {
                        $query->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                    });
            })
            ->count();

        Log::info('Checking existing bookings', [
            'bookings_count' => $bookings
        ]);

        if ($bookings > 0) {
            return [
                'available' => false,
                'message' => 'Accommodation is not available for the specified dates',
                'reason' => 'existing_booking'
            ];
        }

        // Check for unavailable periods using AvailabilityChecker
        if ($this->availabilityChecker->hasAnyConflict($accommodation, $startDate, $endDate)) {
            return [
                'available' => false,
                'message' => 'Accommodation is not available for the specified dates',
                'reason' => 'blocked_period'
            ];
        }

        // Get occupancy from request or use minimum occupancy
        $occupancy = $requestedOccupancy ?? $accommodation->min_occupancy;

        // Validate occupancy against accommodation limits
        if ($occupancy < $accommodation->min_occupancy) {
            return [
                'available' => false,
                'message' => 'Requested occupancy is below minimum required',
                'reason' => 'occupancy_limit',
                'details' => [
                    'requested_occupancy' => $occupancy,
                    'min_occupancy' => $accommodation->min_occupancy,
                    'max_occupancy' => $accommodation->max_occupancy
                ]
            ];
        }

        if ($occupancy > $accommodation->max_occupancy) {
            return [
                'available' => false,
                'message' => 'Requested occupancy exceeds maximum allowed',
                'reason' => 'occupancy_limit',
                'details' => [
                    'requested_occupancy' => $occupancy,
                    'min_occupancy' => $accommodation->min_occupancy,
                    'max_occupancy' => $accommodation->max_occupancy
                ]
            ];
        }

        // Calculate daily prices and total based on nights, not days
        $currentDate = new \DateTime($startDate);
        $endDateTime = new \DateTime($endDate);
        $dailyPrices = [];
        $totalPrice = 0;
        $totalOriginalPrice = 0;
        $appliedDiscounts = [];
        $daysInAdvance = now()->diffInDays($currentDate);
        $lengthOfStay = $currentDate->diff($endDateTime)->days;

        Log::info('Starting price calculations', [
            'start_date' => $currentDate->format('Y-m-d'),
            'end_date' => $endDateTime->format('Y-m-d'),
            'days_in_advance' => $daysInAdvance,
            'length_of_stay' => $lengthOfStay
        ]);

        try {
            Log::info('Calculating prices', [
                'length_of_stay' => $lengthOfStay,
                'days_in_advance' => $daysInAdvance
            ]);

            // Only iterate until the day before the end date (to count nights, not days)
            while ($currentDate < $endDateTime) {
                $dateStr = $currentDate->format('Y-m-d');
                
                try {
                    // Get price with discounts applied
                    $priceInfo = $accommodation->getFinalPrice($dateStr, $occupancy, $lengthOfStay, $daysInAdvance);
                    
                    Log::info('Daily price calculation', [
                        'date' => $dateStr,
                        'price_info' => $priceInfo
                    ]);
                } catch (\Exception $e) {
                    Log::error('Error calculating daily price', [
                        'date' => $dateStr,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
                
                // Store both original and discounted prices
                $dailyPrices[$dateStr] = [
                    'original_price' => $priceInfo['originalPrice'] ?? 0,
                    'final_price' => $priceInfo['price'] ?? 0,
                    'discount' => $priceInfo['discount'] ?? 0,
                    'discount_percentage' => $priceInfo['discountPercentage'] ?? 0
                ];
                
                $totalPrice += $priceInfo['price'];
                $totalOriginalPrice += $priceInfo['originalPrice'];
                
                // Track applied discounts
                if ($priceInfo['discountPercentage'] > 0) {
                    $appliedDiscounts[] = [
                        'date' => $dateStr,
                        'discount_percentage' => $priceInfo['discountPercentage'],
                        'discount_amount' => $priceInfo['discount']
                    ];
                }
                
                $currentDate->modify('+1 day');
            }

            Log::info('Price calculation complete', [
                'total_price' => $totalPrice,
                'total_original_price' => $totalOriginalPrice,
                'discount_count' => count($appliedDiscounts)
            ]);

            // Log successful search with price
            \App\Models\AccommodationSearch::create([
                ...$searchData,
                'was_available' => true,
                'quoted_price' => $totalPrice,
                'original_price' => $totalOriginalPrice,
                'applied_discounts' => $appliedDiscounts
            ]);

            Log::info('Preparing response');
            $response = [
                'available' => true,
                'message' => 'Accommodation is available for the specified dates',
                'total_price' => $totalPrice,
                'pricing' => [
                    'daily_prices' => $dailyPrices,
                    'original_price' => $totalOriginalPrice,
                    'total_price' => $totalPrice,
                    'total_discount' => $totalOriginalPrice - $totalPrice,
                    'applied_discounts' => $appliedDiscounts,
                    'currency' => 'R',
                    'occupancy' => $occupancy,
                    'min_occupancy' => $accommodation->min_occupancy,
                    'max_occupancy' => $accommodation->max_occupancy
                ]
            ];
            Log::info('Sending response', ['response' => $response]);

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('Error in price calculation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}