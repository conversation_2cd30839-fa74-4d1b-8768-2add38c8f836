<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Accommodation;
use App\Models\Booking;
use App\Models\BookingStatus;
use App\Services\BookingPriceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Bookings",
 *     description="API endpoints for managing bookings"
 * )
 */
class BookingApiController extends Controller
{
    /**
     * Create a new booking.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @OA\Post(
     *     path="/api/bookings",
     *     summary="Create a new booking",
     *     description="Creates a new booking for an accommodation",
     *     operationId="createBooking",
     *     tags={"Bookings"},
     *     security={{"sanctum": {"widget-access"}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"accommodation_id", "email", "contact_number", "first_name", "last_name", "start_date", "end_date", "occupancy"},
     *             @OA\Property(property="accommodation_id", type="integer", example=1),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="contact_number", type="string", example="+27123456789"),
     *             @OA\Property(property="first_name", type="string", example="John"),
     *             @OA\Property(property="last_name", type="string", example="Doe"),
     *             @OA\Property(property="start_date", type="string", format="date", example="2023-06-01"),
     *             @OA\Property(property="end_date", type="string", format="date", example="2023-06-05"),
     *             @OA\Property(property="occupancy", type="integer", example=2),
     *             @OA\Property(property="notes", type="string", example="Special requests or additional information about the booking.", nullable=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Booking created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Booking created successfully"),
     *             @OA\Property(property="booking", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="accommodation_id", type="integer", example=1),
     *                 @OA\Property(property="user_id", type="integer", example=1),
     *                 @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                 @OA\Property(property="contact_number", type="string", example="+27123456789"),
     *                 @OA\Property(property="first_name", type="string", example="John"),
     *                 @OA\Property(property="last_name", type="string", example="Doe"),
     *                 @OA\Property(property="start_date", type="string", format="date", example="2023-06-01"),
     *                 @OA\Property(property="end_date", type="string", format="date", example="2023-06-05"),
     *                 @OA\Property(property="occupancy", type="integer", example=2),
     *                 @OA\Property(property="total_price", type="number", format="float", example=1250.00),
     *                 @OA\Property(property="booking_status_id", type="integer", example=1),
     *                 @OA\Property(property="notes", type="string", example="Special requests or additional information about the booking.", nullable=true),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid input",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Validation failed"),
     *             @OA\Property(property="errors", type="object",
     *                 @OA\Property(property="accommodation_id", type="array", @OA\Items(type="string"), example={"The accommodation id field is required."}),
     *                 @OA\Property(property="start_date", type="array", @OA\Items(type="string"), example={"The start date field is required."})
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Accommodation not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation not found or you do not have permission to access it")
     *         )
     *     ),
     *     @OA\Response(
     *         response=409,
     *         description="Accommodation not available",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Accommodation is not available for the specified dates"),
     *             @OA\Property(property="reason", type="string", example="existing_booking")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Unauthenticated.")
     *         )
     *     )
     * )
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'accommodation_id' => 'required|exists:accommodations,id',
            'email' => 'required|email',
            'contact_number' => 'required|string|max:20',
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'occupancy' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Find accommodation and ensure it belongs to the authenticated user or their team and is published
        $accommodation = Accommodation::published()
            ->with('group') // Load the group relationship for pricing fallback
            ->where('id', $request->accommodation_id)
            ->where(function ($query) use ($user, $teamIds) {
                $query->where('user_id', $user->id);

                if (!empty($teamIds)) {
                    $query->orWhereIn('team_id', $teamIds);
                }
            })
            ->first();

        if (!$accommodation) {
            return response()->json([
                'message' => 'Accommodation not found or you do not have permission to access it'
            ], 404);
        }

        // Check if the accommodation is available for the requested dates
        $availabilityCheck = $this->checkAvailability($accommodation, $request->start_date, $request->end_date, $request->occupancy);

        if (!$availabilityCheck['available']) {
            return response()->json([
                'message' => $availabilityCheck['message'],
                'reason' => $availabilityCheck['reason'] ?? 'unavailable'
            ], 409);
        }

        // Get the default booking status (e.g., "Pending")
        $defaultStatus = BookingStatus::where('name', 'Pending')->first();
        if (!$defaultStatus) {
            $defaultStatus = BookingStatus::first(); // Fallback to first status if "Pending" doesn't exist
        }

        // Create the booking
        $booking = new Booking([
            'accommodation_id' => $accommodation->id,
            'user_id' => $user->id,
            'email' => $request->email,
            'contact_number' => $request->contact_number,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'occupancy' => $request->occupancy,
            'booking_status_id' => $defaultStatus->id,
            'notes' => $request->notes,
        ]);

        // Calculate the total price
        try {
            $priceService = app(BookingPriceService::class);
            $booking->total_price = $priceService->calculateTotalPrice($booking);

            // Save the booking
            $booking->save();
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'reason' => 'price_calculation_error'
            ], 422);
        }

        return response()->json([
            'message' => 'Booking created successfully',
            'booking' => $booking
        ], 201);
    }

    /**
     * Helper method to check accommodation availability.
     *
     * @param Accommodation $accommodation
     * @param string $startDate
     * @param string $endDate
     * @param int $occupancy
     * @return array
     */
    private function checkAvailability(Accommodation $accommodation, string $startDate, string $endDate, int $occupancy)
    {
        // Check minimum booking notice
        if ($accommodation->minimum_booking_notice > 0) {
            $today = now()->startOfDay();
            $bookingStartDate = \Carbon\Carbon::parse($startDate)->startOfDay();
            $daysUntilBooking = $today->diffInDays($bookingStartDate);

            if ($daysUntilBooking < $accommodation->minimum_booking_notice) {
                return [
                    'available' => false,
                    'message' => "Booking must be made at least {$accommodation->minimum_booking_notice} days in advance",
                    'reason' => 'minimum_notice'
                ];
            }
        }

        // Check if there are any bookings
        $bookings = Booking::where('accommodation_id', $accommodation->id)
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate])
                    ->orWhere(function ($query) use ($startDate, $endDate) {
                        $query->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                    });
            })
            ->count();

        if ($bookings > 0) {
            return [
                'available' => false,
                'message' => 'Accommodation is not available for the specified dates',
                'reason' => 'existing_booking'
            ];
        }

        // Check for unavailable periods
        $unavailablePeriod = \App\Models\AccommodationUnavailablePeriod::where('accommodation_id', $accommodation->id)
            ->where('active', true)
            ->where(function ($query) use ($startDate, $endDate) {
                // Check date range periods
                $query->where('type', 'date_range')
                    ->where(function ($q) use ($startDate, $endDate) {
                        $q->whereBetween('start_date', [$startDate, $endDate])
                            ->orWhereBetween('end_date', [$startDate, $endDate])
                            ->orWhere(function ($q) use ($startDate, $endDate) {
                                $q->where('start_date', '<=', $startDate)
                                    ->where('end_date', '>=', $endDate);
                            });
                    });
            })
            ->first();

        if ($unavailablePeriod) {
            return [
                'available' => false,
                'message' => 'Accommodation is not available for the specified dates',
                'reason' => 'blocked_period'
            ];
        }

        // Validate occupancy against accommodation limits
        if ($occupancy > $accommodation->max_occupancy) {
            return [
                'available' => false,
                'message' => 'Requested occupancy exceeds maximum allowed',
                'reason' => 'occupancy_limit'
            ];
        }

        // Check if the accommodation has a default price set
        $hasDefaultPrice = $accommodation->prices()->where('type', 'default')->exists();

        // If no individual default price, check if the accommodation belongs to a group with a default price
        if (!$hasDefaultPrice && $accommodation->accommodation_group_id) {
            $hasGroupDefaultPrice = $accommodation->group->prices()
                ->where('type', 'default')
                ->exists();

            // Use group pricing if available
            $hasDefaultPrice = $hasGroupDefaultPrice;
        }

        if (!$hasDefaultPrice) {
            return [
                'available' => false,
                'message' => 'This accommodation does not have either an individual default price or a group default price. Bookings cannot be made until pricing is configured.',
                'reason' => 'no_price_configured'
            ];
        }

        return [
            'available' => true,
            'message' => 'Accommodation is available for the specified dates'
        ];
    }
}
