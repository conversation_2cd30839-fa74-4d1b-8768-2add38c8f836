<?php

namespace App\Http\Controllers;

use App\Models\Accommodation;
use App\Models\AccommodationUnavailablePeriod;
use App\Models\Booking;
use App\Models\BookingStatus;
use App\Services\BookingPriceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class BookingsController extends Controller
{
    /**
     * Display a listing of all bookings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Get query parameters for filtering and sorting
        $accommodationId = $request->input('accommodation_id');
        $sortField = $request->input('sort_field', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');

        // Start building the query
        $query = Booking::query()
            ->with(['accommodation', 'status'])
            ->whereHas('accommodation', function($query) use ($user, $teamIds) {
                // Only show bookings for accommodations owned by the user or their team
                $query->where('user_id', $user->id)
                      ->orWhereIn('team_id', $teamIds);
            });

        // Apply accommodation filter if provided
        if ($accommodationId) {
            $query->where('accommodation_id', $accommodationId);
        }

        // Apply sorting
        $query->orderBy($sortField, $sortDirection);

        // Get all accommodations for the filter dropdown
        $accommodations = Accommodation::where('user_id', $user->id)
            ->orWhereIn('team_id', $teamIds)
            ->orderBy('name')
            ->get(['id', 'name']);

        // Get all booking statuses
        $bookingStatuses = BookingStatus::all();

        // Paginate the results
        $bookings = $query->paginate(10)->withQueryString();

        return Inertia::render('Bookings/Index', [
            'bookings' => $bookings,
            'accommodations' => $accommodations,
            'bookingStatuses' => $bookingStatuses,
            'filters' => [
                'accommodation_id' => $accommodationId,
                'sort_field' => $sortField,
                'sort_direction' => $sortDirection,
            ],
        ]);
    }

    /**
     * Show the form for creating a new booking.
     *
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Inertia\Response
     */
    public function create(Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        // Load the booking statuses
        $bookingStatuses = BookingStatus::all();

        // Load existing bookings for the accommodation
        $bookings = Booking::where('accommodation_id', $accommodation->id)
            ->select('id', 'start_date', 'end_date')
            ->get()
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'start' => $booking->start_date,
                    'end' => $booking->end_date,
                ];
            });

        // Load the accommodation's prices and group for local price calculation
        $accommodation->load(['prices', 'group.prices']);

        return Inertia::render('Bookings/Create', [
            'accommodation' => $accommodation,
            'bookingStatuses' => $bookingStatuses,
            'existingBookings' => $bookings,
        ]);
    }

    /**
     * Store a newly created booking in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Accommodation  $accommodation
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request, Accommodation $accommodation)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Check if the accommodation belongs to the authenticated user or their team
        if ($accommodation->user_id !== $user->id && !in_array($accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        // Validate the request
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'contact_number' => 'required|string|max:20',
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'occupancy' => [
                'required',
                'integer',
                'min:' . ($accommodation->min_occupancy ?? 1),
                'max:' . ($accommodation->max_occupancy ?? 10)
            ],
            'booking_status_id' => 'required|exists:booking_statuses,id',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Check minimum booking notice
        if ($accommodation->minimum_booking_notice > 0) {
            $today = now()->startOfDay();
            $bookingStartDate = \Carbon\Carbon::parse($request->start_date)->startOfDay();
            $daysUntilBooking = $today->diffInDays($bookingStartDate);

            if ($daysUntilBooking < $accommodation->minimum_booking_notice) {
                return redirect()->back()->withErrors([
                    'start_date' => "Booking must be made at least {$accommodation->minimum_booking_notice} days in advance"
                ])->withInput();
            }
        }

        // Check if the accommodation is available for the specified dates
        $bookings = Booking::where('accommodation_id', $accommodation->id)
            ->where(function ($query) use ($request) {
                $query->whereBetween('start_date', [$request->start_date, $request->end_date])
                    ->orWhereBetween('end_date', [$request->start_date, $request->end_date])
                    ->orWhere(function ($query) use ($request) {
                        $query->where('start_date', '<=', $request->start_date)
                            ->where('end_date', '>=', $request->end_date);
                    });
            })
            ->count();

        if ($bookings > 0) {
            return redirect()->back()->withErrors([
                'start_date' => 'Accommodation is not available for the specified dates due to existing bookings'
            ])->withInput();
        }

        // Check for unavailable periods
        $hasUnavailablePeriod = AccommodationUnavailablePeriod::hasUnavailablePeriod(
            $accommodation->id,
            $request->start_date,
            $request->end_date
        );

        if ($hasUnavailablePeriod) {
            return redirect()->back()->withErrors([
                'start_date' => 'Accommodation is not available for the specified dates due to unavailable periods'
            ])->withInput();
        }

        // Check if the accommodation has a default price set
        $hasDefaultPrice = $accommodation->prices()->where('type', 'default')->exists();

        if (!$hasDefaultPrice) {
            return redirect()->back()->withErrors([
                'start_date' => 'This accommodation does not have a default price set. Bookings cannot be made until pricing is configured.'
            ])->withInput();
        }

        // Create the booking
        $booking = new Booking([
            'accommodation_id' => $accommodation->id,
            'user_id' => $user->id,
            'email' => $request->email,
            'contact_number' => $request->contact_number,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'occupancy' => $request->occupancy,
            'booking_status_id' => $request->booking_status_id,
            'notes' => $request->notes,
        ]);

        // Calculate the total price
        try {
            $priceService = app(BookingPriceService::class);
            $booking->total_price = $priceService->calculateTotalPrice($booking);

            // Save the booking
            $booking->save();
        } catch (\Exception $e) {
            return redirect()->back()->withErrors([
                'start_date' => $e->getMessage()
            ])->withInput();
        }

        return redirect()->route('accommodations.show', $accommodation->id)
            ->with('success', 'Booking created successfully.');
    }

    /**
     * Display the specified booking.
     *
     * @param  \App\Models\Booking  $booking
     * @return \Inertia\Response
     */
    public function show(Booking $booking)
    {
        $user = Auth::user();
        $teamIds = $user->allTeams()->pluck('id')->toArray();

        // Load the accommodation with its owner
        $booking->load(['accommodation.user', 'status']);

        // Check if the booking's accommodation belongs to the authenticated user or their team
        if ($booking->accommodation->user_id !== $user->id && !in_array($booking->accommodation->team_id, $teamIds)) {
            abort(403, 'Unauthorized action.');
        }

        // Get all booking statuses for status updates
        $bookingStatuses = BookingStatus::all();

        return Inertia::render('Bookings/Show', [
            'booking' => $booking,
            'bookingStatuses' => $bookingStatuses,
        ]);
    }
}
